"""
Mock FastAPI server that replays recorded WebSocket messages for the shortage workflow.

The server exposes REST APIs for frontend developers to inspect the captured dataset and
provides a WebSocket endpoint that replays the sequence with recorded delays.
"""

from __future__ import annotations

import asyncio
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

from fastapi import Fast<PERSON><PERSON>, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

RECORDING_PATH = Path(__file__).parent / "mock_recordings" / "shortage_workflow_messages.json"


class RecordedMessage(BaseModel):
    """Single WebSocket frame captured during the original run."""

    id: str
    sequence: int
    timestamp: str
    direction: str = Field(pattern=r"^(incoming|outgoing)$")
    transport: str
    type: str
    payload: Dict[str, Any]
    delay_seconds: float = 0.0


class RecordingEnvelope(BaseModel):
    """Envelope describing the full capture artifact."""

    recording_id: str
    metadata: Dict[str, Any]
    messages: List[RecordedMessage]

    def sorted_messages(self) -> List[RecordedMessage]:
        return sorted(self.messages, key=lambda msg: (msg.sequence, msg.timestamp))


def load_recording(path: Path) -> RecordingEnvelope:
    if not path.exists():
        raise FileNotFoundError(f"Recording file not found: {path}")
    with path.open("r", encoding="utf-8") as handle:
        raw = json.load(handle)
    return RecordingEnvelope.model_validate(raw)


class ReplayManager:
    """Orchestrates replaying the recorded messages to connected clients."""

    def __init__(self, recording: RecordingEnvelope) -> None:
        self._recording = recording
        self._connections: Set[WebSocket] = set()
        self._replay_lock = asyncio.Lock()
        self._active_task: Optional[asyncio.Task[None]] = None

    @property
    def messages(self) -> List[RecordedMessage]:
        return self._recording.sorted_messages()

    def connection_count(self) -> int:
        return len(self._connections)

    async def register(self, websocket: WebSocket) -> None:
        self._connections.add(websocket)

    async def unregister(self, websocket: WebSocket) -> None:
        self._connections.discard(websocket)

    async def start_replay(self) -> None:
        """Begin replaying messages to every active connection."""
        async with self._replay_lock:
            if self._active_task and not self._active_task.done():
                raise RuntimeError("Replay already in progress")
            self._active_task = asyncio.create_task(self._run_replay())

    async def _run_replay(self) -> None:
        if not self._connections:
            return

        for message in self.messages:
            await asyncio.sleep(max(message.delay_seconds, 0.0))
            payload = {
                "id": message.id,
                "sequence": message.sequence,
                "timestamp": message.timestamp,
                "direction": message.direction,
                "type": message.type,
                "payload": message.payload,
            }
            await self._broadcast(payload)

    async def _broadcast(self, payload: Dict[str, Any]) -> None:
        disconnected: List[WebSocket] = []
        for websocket in list(self._connections):
            try:
                await websocket.send_json(payload)
            except (WebSocketDisconnect, RuntimeError):
                disconnected.append(websocket)
        for websocket in disconnected:
            self._connections.discard(websocket)


recording = load_recording(RECORDING_PATH)
replay_manager = ReplayManager(recording)

app = FastAPI(title="Mock Financial Analyzer Replay Server")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/api/messages")
async def list_messages() -> Dict[str, Any]:
    """Return the full recorded dataset for inspection/search."""
    return {
        "recording_id": recording.recording_id,
        "metadata": recording.metadata,
        "messages": [message.model_dump() for message in recording.sorted_messages()],
    }


@app.get("/api/messages/{message_id}")
async def get_message(message_id: str) -> RecordedMessage:
    for message in recording.sorted_messages():
        if message.id == message_id:
            return message
    raise HTTPException(status_code=404, detail=f"Message '{message_id}' not found")


@app.get("/api/replay/start")
async def start_replay() -> Dict[str, Any]:
    """Trigger the WebSocket replay in the background."""
    if replay_manager.connection_count() == 0:
        raise HTTPException(status_code=409, detail="No active WebSocket clients to replay to")

    try:
        await replay_manager.start_replay()
    except RuntimeError as exc:  # replay already running
        raise HTTPException(status_code=409, detail=str(exc)) from exc
    else:
        return {"status": "started", "connections": replay_manager.connection_count()}


@app.get("/api/replay/status")
async def replay_status() -> Dict[str, Any]:
    """Expose quick status for tooling/debugging."""
    task = replay_manager._active_task
    state = "idle"
    if task is not None:
        state = "running" if not task.done() else "completed"
    return {
        "connections": replay_manager.connection_count(),
        "state": state,
        "recording_id": recording.recording_id,
    }


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket) -> None:
    await websocket.accept()
    await replay_manager.register(websocket)
    try:
        while True:
            await websocket.receive_text()
    except WebSocketDisconnect:
        pass
    finally:
        await replay_manager.unregister(websocket)


@app.get("/")
async def root() -> Dict[str, Any]:
    return {
        "message": "Mock Financial Analyzer Replay Server",
        "recording_id": recording.recording_id,
        "endpoints": {
            "list_messages": "/api/messages",
            "get_message": "/api/messages/{message_id}",
            "websocket": "/ws",
            "start_replay": "/api/replay/start",
            "replay_status": "/api/replay/status",
        },
    }
